"use client"

import { useState } from "react"
import { BarChart3, Bell, Calendar, FileText, Home, Menu, Search, Settings, Users, ChevronDown } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet"

export default function RTLDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const SidebarContent = () => (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <BarChart3 className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">نظام الإدارة</h1>
            <p className="text-sm text-gray-500">الإصدار 2.1</p>
          </div>
        </div>
      </div>

      <nav className="flex-1 p-6 space-y-2">
        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-blue-700 bg-blue-50 rounded-lg font-medium"
          onClick={() => setSidebarOpen(false)}
        >
          <Home className="h-5 w-5" />
          <span>لوحة التحكم الرئيسية</span>
        </Link>

        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setSidebarOpen(false)}
        >
          <BarChart3 className="h-5 w-5" />
          <span>التقارير والإحصائيات</span>
        </Link>

        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setSidebarOpen(false)}
        >
          <Users className="h-5 w-5" />
          <span>إدارة العملاء</span>
        </Link>

        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setSidebarOpen(false)}
        >
          <FileText className="h-5 w-5" />
          <span>الطلبات والفواتير</span>
        </Link>

        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setSidebarOpen(false)}
        >
          <Calendar className="h-5 w-5" />
          <span>التقويم والمواعيد</span>
        </Link>

        <Link
          href="#"
          className="flex items-center gap-3 px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setSidebarOpen(false)}
        >
          <Settings className="h-5 w-5" />
          <span>الإعدادات العامة</span>
        </Link>
      </nav>

      {/* Quick Stats في الشريط الجانبي */}
      <div className="p-6 border-t border-gray-200">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
          <h3 className="text-sm font-semibold text-gray-900 mb-4">الإحصائيات السريعة</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">زوار اليوم</span>
              <span className="text-sm font-bold text-blue-600">2,847</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">طلبات جديدة</span>
              <span className="text-sm font-bold text-green-600">156</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إيرادات اليوم</span>
              <span className="text-sm font-bold text-purple-600">18,450 ر.س</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 font-arabic" dir="rtl">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 lg:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Mobile Menu Button */}
            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80 p-0">
                <SheetTitle className="sr-only">قائمة التنقل</SheetTitle>
                <SidebarContent />
              </SheetContent>
            </Sheet>

            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input placeholder="البحث في النظام..." className="w-48 sm:w-64 lg:w-80 pr-10 text-right" />
            </div>
          </div>

          <div className="flex items-center gap-2 lg:gap-4">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
            </Button>
            <div className="hidden sm:flex items-center gap-3">
              <div className="text-right">
                <p className="text-sm font-medium">أحمد محمد علي</p>
                <p className="text-xs text-gray-500">مدير النظام</p>
              </div>
              <Image
                src="/placeholder.svg?height=40&width=40"
                alt="صورة المستخدم"
                width={40}
                height={40}
                className="rounded-full"
              />
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </div>
            {/* Mobile user avatar */}
            <div className="sm:hidden">
              <Image
                src="/placeholder.svg?height=32&width=32"
                alt="صورة المستخدم"
                width={32}
                height={32}
                className="rounded-full"
              />
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:block w-72 bg-white border-l border-gray-200 min-h-screen">
          <SidebarContent />
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          <div className="mb-6 lg:mb-8">
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">مرحباً بك، أحمد</h1>
            <p className="text-gray-600 text-sm lg:text-base">إليك نظرة عامة على أداء نشاطك التجاري اليوم</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
            <Card className="border-r-4 border-r-blue-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">إجمالي المبيعات</CardTitle>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-4 w-4 lg:h-5 lg:w-5 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl lg:text-2xl font-bold text-gray-900">245,680 ر.س</div>
                <p className="text-xs lg:text-sm text-green-600 font-medium">+12.5% من الشهر الماضي</p>
              </CardContent>
            </Card>

            <Card className="border-r-4 border-r-green-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">العملاء الجدد</CardTitle>
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="h-4 w-4 lg:h-5 lg:w-5 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl lg:text-2xl font-bold text-gray-900">3,847</div>
                <p className="text-xs lg:text-sm text-green-600 font-medium">+8.2% من الشهر الماضي</p>
              </CardContent>
            </Card>

            <Card className="border-r-4 border-r-orange-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">الطلبات المكتملة</CardTitle>
                <div className="p-2 bg-orange-100 rounded-lg">
                  <FileText className="h-4 w-4 lg:h-5 lg:w-5 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl lg:text-2xl font-bold text-gray-900">1,429</div>
                <p className="text-xs lg:text-sm text-green-600 font-medium">+15.3% من الشهر الماضي</p>
              </CardContent>
            </Card>

            <Card className="border-r-4 border-r-purple-500">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">معدل التحويل</CardTitle>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-4 w-4 lg:h-5 lg:w-5 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xl lg:text-2xl font-bold text-gray-900">%24.8</div>
                <p className="text-xs lg:text-sm text-green-600 font-medium">+2.1% من الشهر الماضي</p>
              </CardContent>
            </Card>
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
            {/* Recent Activity */}
            <div className="xl:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg lg:text-xl">النشاط الأخير</CardTitle>
                  <CardDescription>آخر العمليات والأنشطة في النظام</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 lg:space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-3 h-3 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <p className="font-medium text-gray-900 text-sm lg:text-base">طلب جديد من سارة أحمد محمد</p>
                          <Badge variant="secondary" className="bg-green-100 text-green-700 self-start">
                            مكتمل
                          </Badge>
                        </div>
                        <p className="text-xs lg:text-sm text-gray-500 mt-1">طلب شراء بقيمة 1,250 ر.س - منذ 5 دقائق</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-3 h-3 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <p className="font-medium text-gray-900 text-sm lg:text-base">
                            تم تحديث بيانات المنتج #A1234
                          </p>
                          <Badge variant="outline" className="self-start">
                            محدث
                          </Badge>
                        </div>
                        <p className="text-xs lg:text-sm text-gray-500 mt-1">تحديث الأسعار والمواصفات - منذ 15 دقيقة</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-3 h-3 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <p className="font-medium text-gray-900 text-sm lg:text-base">
                            عميل جديد: محمد عبدالله الأحمد
                          </p>
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700 self-start">
                            عضو جديد
                          </Badge>
                        </div>
                        <p className="text-xs lg:text-sm text-gray-500 mt-1">
                          انضم للنظام وأكمل التسجيل - منذ 30 دقيقة
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-3 h-3 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <p className="font-medium text-gray-900 text-sm lg:text-base">تم إلغاء الطلب #ORD-5678</p>
                          <Badge variant="destructive" className="self-start">
                            ملغي
                          </Badge>
                        </div>
                        <p className="text-xs lg:text-sm text-gray-500 mt-1">إلغاء بناءً على طلب العميل - منذ ساعة</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-3 h-3 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                          <p className="font-medium text-gray-900 text-sm lg:text-base">
                            تم إنشاء تقرير المبيعات الشهري
                          </p>
                          <Badge variant="secondary" className="bg-purple-100 text-purple-700 self-start">
                            تقرير
                          </Badge>
                        </div>
                        <p className="text-xs lg:text-sm text-gray-500 mt-1">
                          تقرير شامل لشهر ديسمبر 2024 - منذ ساعتين
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Upcoming Tasks */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg lg:text-xl">المهام القادمة</CardTitle>
                  <CardDescription>المهام والمواعيد المجدولة</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <Calendar className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm lg:text-base">اجتماع فريق التطوير</p>
                        <p className="text-xs lg:text-sm text-gray-500">غداً - 10:00 صباحاً</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                      <FileText className="h-5 w-5 text-green-600 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm lg:text-base">مراجعة التقارير الشهرية</p>
                        <p className="text-xs lg:text-sm text-gray-500">الخميس - 2:00 مساءً</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                      <Users className="h-5 w-5 text-orange-600 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm lg:text-base">عرض تقديمي للعملاء</p>
                        <p className="text-xs lg:text-sm text-gray-500">الجمعة - 11:00 صباحاً</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                      <Settings className="h-5 w-5 text-purple-600 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 text-sm lg:text-base">تدريب الموظفين الجدد</p>
                        <p className="text-xs lg:text-sm text-gray-500">الأحد - 9:00 صباحاً</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
