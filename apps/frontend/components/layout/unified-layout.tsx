'use client';

import { ReactNode } from 'react';
import { UnifiedSidebar } from './unified-sidebar';
import { UnifiedHeader } from './unified-header';
import { UnifiedFooter } from './unified-footer';
import { cn } from '@/lib/utils';

interface UnifiedLayoutProps {
  children: ReactNode;
  className?: string;
  showSidebar?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
}

export function UnifiedLayout({ 
  children, 
  className,
  showSidebar = true,
  showHeader = true,
  showFooter = true
}: UnifiedLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 font-arabic" dir="rtl">
      {/* Header */}
      {showHeader && <UnifiedHeader />}

      <div className="flex">
        {/* Desktop Sidebar */}
        {showSidebar && (
          <aside className="hidden lg:block w-72 bg-white border-l border-gray-200 min-h-screen">
            <UnifiedSidebar />
          </aside>
        )}

        {/* Main Content */}
        <main className={cn("flex-1 flex flex-col", className)}>
          {/* Content Area */}
          <div className="flex-1 p-3 sm:p-4 lg:p-8">
            {children}
          </div>

          {/* Footer */}
          {showFooter && <UnifiedFooter />}
        </main>
      </div>
    </div>
  );
}
