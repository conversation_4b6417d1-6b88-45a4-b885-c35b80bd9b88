'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  UserPlus, 
  Shield,
  Settings,
  Mail,
  Phone,
  Calendar,
  Edit,
  Trash2,
  MoreVertical
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function UsersPage() {
  // Demo users data
  const users = [
    {
      id: 1,
      firstName: 'محمد',
      lastName: 'يوسف',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-15T10:30:00Z',
      createdAt: '2023-01-01T00:00:00Z',
      avatar: null
    },
    {
      id: 2,
      firstName: 'أحمد',
      lastName: 'محمد',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'developer',
      status: 'active',
      lastLogin: '2024-01-15T09:15:00Z',
      createdAt: '2023-01-15T00:00:00Z',
      avatar: null
    },
    {
      id: 3,
      firstName: 'فاطمة',
      lastName: 'علي',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'designer',
      status: 'active',
      lastLogin: '2024-01-14T16:45:00Z',
      createdAt: '2023-02-20T00:00:00Z',
      avatar: null
    },
    {
      id: 4,
      firstName: 'محمد',
      lastName: 'حسن',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'media_buyer',
      status: 'active',
      lastLogin: '2024-01-15T08:20:00Z',
      createdAt: '2023-03-10T00:00:00Z',
      avatar: null
    },
    {
      id: 5,
      firstName: 'سارة',
      lastName: 'أحمد',
      email: '<EMAIL>',
      phone: '+20 ************',
      role: 'wordpress_developer',
      status: 'inactive',
      lastLogin: '2024-01-10T14:30:00Z',
      createdAt: '2023-04-05T00:00:00Z',
      avatar: null
    }
  ];

  const getRoleDisplayName = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: 'مدير النظام',
      sales_manager: 'مدير المبيعات',
      media_buyer: 'مشتري الإعلانات',
      developer: 'مطور',
      designer: 'مصمم',
      wordpress_developer: 'مطور ووردبريس',
    };
    return roleMap[role] || role;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800';
      case 'sales_manager': return 'bg-blue-100 text-blue-800';
      case 'media_buyer': return 'bg-orange-100 text-orange-800';
      case 'developer': return 'bg-green-100 text-green-800';
      case 'designer': return 'bg-pink-100 text-pink-800';
      case 'wordpress_developer': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ أقل من ساعة';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  };

  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]}>
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 overflow-x-hidden">
          {/* Header */}
          <div className="flex flex-col gap-4 md:flex-row md:justify-between md:items-center mb-8">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">إدارة المستخدمين</h1>
              <p className="text-gray-600 mt-1">إدارة حسابات المستخدمين والصلاحيات</p>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
              <Badge variant="outline" className="text-blue-600 border-blue-600 hidden sm:inline-flex">
                قيد التطوير
              </Badge>
              <Button className="w-full sm:w-auto">
                <UserPlus className="h-4 w-4 ml-2" />
                <span className="hidden sm:inline">إضافة مستخدم جديد</span>
                <span className="sm:hidden">إضافة مستخدم</span>
              </Button>
            </div>
          </div>

          {/* User Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المستخدمين</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground">
                  +1 من الشهر الماضي
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المستخدمين النشطين</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {users.filter(u => u.status === 'active').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((users.filter(u => u.status === 'active').length / users.length) * 100)}% من الإجمالي
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المديرين</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {users.filter(u => u.role === 'admin').length}
                </div>
                <p className="text-xs text-muted-foreground">
                  صلاحيات كاملة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">آخر تسجيل دخول</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">اليوم</div>
                <p className="text-xs text-muted-foreground">
                  {users.filter(u => {
                    const lastLogin = new Date(u.lastLogin);
                    const today = new Date();
                    return lastLogin.toDateString() === today.toDateString();
                  }).length} مستخدم
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Users List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                قائمة المستخدمين
              </CardTitle>
              <CardDescription>
                جميع المستخدمين المسجلين في النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Desktop and Tablet View */}
              <div className="hidden md:block space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                        {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                      </div>
                      <div>
                        <h4 className="font-medium text-lg">{user.firstName} {user.lastName}</h4>
                        <div className="flex items-center gap-4 mt-1">
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Mail className="h-3 w-3" />
                            <span className="truncate max-w-[200px]">{user.email}</span>
                          </div>
                          <div className="hidden lg:flex items-center gap-1 text-sm text-gray-600">
                            <Phone className="h-3 w-3" />
                            {user.phone}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge className={getRoleColor(user.role)}>
                            {getRoleDisplayName(user.role)}
                          </Badge>
                          <Badge className={getStatusColor(user.status)}>
                            {user.status === 'active' && 'نشط'}
                            {user.status === 'inactive' && 'غير نشط'}
                            {user.status === 'suspended' && 'موقوف'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="hidden lg:block text-center">
                        <div className="text-sm font-medium text-gray-900">آخر دخول</div>
                        <p className="text-xs text-gray-500">{formatLastLogin(user.lastLogin)}</p>
                      </div>
                      <div className="hidden lg:block text-center">
                        <div className="text-sm font-medium text-gray-900">تاريخ الانضمام</div>
                        <p className="text-xs text-gray-500">{formatDate(user.createdAt)}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3 ml-1" />
                          <span className="hidden lg:inline">تعديل</span>
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-3 w-3 ml-1" />
                          <span className="hidden lg:inline">حذف</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Mobile View */}
              <div className="md:hidden space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start gap-3 mb-3">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold flex-shrink-0">
                        {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-lg truncate">{user.firstName} {user.lastName}</h4>
                        <div className="flex items-center gap-2 mt-1 flex-wrap">
                          <Badge className={getRoleColor(user.role)}>
                            {getRoleDisplayName(user.role)}
                          </Badge>
                          <Badge className={getStatusColor(user.status)}>
                            {user.status === 'active' && 'نشط'}
                            {user.status === 'inactive' && 'غير نشط'}
                            {user.status === 'suspended' && 'موقوف'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate">{user.email}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="h-3 w-3 flex-shrink-0" />
                        <span>{user.phone}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 ml-1" />
                        تعديل
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1 text-red-600 hover:text-red-700">
                        <Trash2 className="h-3 w-3 ml-1" />
                        حذف
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Development Notice */}
          <Card className="mt-8 border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Shield className="h-4 w-4 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-semibold text-blue-900">قيد التطوير</h3>
                  <p className="text-blue-700 text-sm break-words">
                    هذه الصفحة قيد التطوير. سيتم إضافة المزيد من الميزات قريباً مثل إدارة الأذونات المتقدمة، تسجيل الأنشطة، وإعدادات الأمان.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    </RoleGuard>
  );
}
