'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  User, 
  Mail,
  Phone,
  Lock,
  Bell,
  Shield,
  Camera,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useState } from 'react';

export default function ProfileSettingsPage() {
  const { user } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);

  const getRoleDisplayName = (role?: string) => {
    const roleMap: Record<string, string> = {
      admin: 'مدير النظام',
      sales_manager: 'مدير المبيعات',
      media_buyer: 'مشتري الإعلانات',
      developer: 'مطور',
      designer: 'مصمم',
      wordpress_developer: 'مطور ووردبريس',
    };
    return roleMap[role || ''] || role || '';
  };

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إعدادات الملف الشخصي</h1>
            <p className="text-gray-600 mt-1">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-blue-600 border-blue-600">
              قيد التطوير
            </Badge>
            <Button>
              <Save className="h-4 w-4 ml-2" />
              حفظ التغييرات
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {/* Profile Picture */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5" />
                الصورة الشخصية
              </CardTitle>
              <CardDescription>
                تحديث صورتك الشخصية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-6">
                <div className="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                  {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                </div>
                <div>
                  <Button variant="outline">
                    <Camera className="h-4 w-4 ml-2" />
                    تغيير الصورة
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">
                    JPG, PNG أو GIF. الحد الأقصى 2MB.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                المعلومات الشخصية
              </CardTitle>
              <CardDescription>
                معلوماتك الأساسية والتواصل
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first-name">الاسم الأول</Label>
                  <Input id="first-name" defaultValue={user?.firstName || ''} />
                </div>
                <div>
                  <Label htmlFor="last-name">الاسم الأخير</Label>
                  <Input id="last-name" defaultValue={user?.lastName || ''} />
                </div>
              </div>
              
              <div>
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input id="email" type="email" defaultValue={user?.email || ''} />
              </div>

              <div>
                <Label htmlFor="phone">رقم الهاتف</Label>
                <Input id="phone" defaultValue={user?.phone || ''} />
              </div>

              <div>
                <Label htmlFor="role">الدور الوظيفي</Label>
                <Input id="role" defaultValue={getRoleDisplayName(user?.role)} disabled />
              </div>

              <div>
                <Label htmlFor="bio">نبذة شخصية</Label>
                <Textarea 
                  id="bio" 
                  placeholder="اكتب نبذة مختصرة عنك..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                الأمان وكلمة المرور
              </CardTitle>
              <CardDescription>
                إعدادات الأمان وتغيير كلمة المرور
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="current-password">كلمة المرور الحالية</Label>
                <div className="relative">
                  <Input 
                    id="current-password" 
                    type={showPassword ? "text" : "password"}
                    placeholder="أدخل كلمة المرور الحالية"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
                <Input 
                  id="new-password" 
                  type="password"
                  placeholder="أدخل كلمة المرور الجديدة"
                />
              </div>

              <div>
                <Label htmlFor="confirm-password">تأكيد كلمة المرور</Label>
                <Input 
                  id="confirm-password" 
                  type="password"
                  placeholder="أعد إدخال كلمة المرور الجديدة"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="two-factor-personal">المصادقة الثنائية</Label>
                  <p className="text-sm text-gray-600">تفعيل المصادقة الثنائية لحسابك</p>
                </div>
                <Switch id="two-factor-personal" />
              </div>
            </CardContent>
          </Card>

          {/* Notification Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                تفضيلات الإشعارات
              </CardTitle>
              <CardDescription>
                اختر أنواع الإشعارات التي تريد استقبالها
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications-profile">إشعارات البريد الإلكتروني</Label>
                  <p className="text-sm text-gray-600">استقبال إشعارات عبر البريد الإلكتروني</p>
                </div>
                <Switch id="email-notifications-profile" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="task-notifications">إشعارات المهام</Label>
                  <p className="text-sm text-gray-600">تنبيهات عند تعيين مهام جديدة</p>
                </div>
                <Switch id="task-notifications" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="project-notifications">إشعارات المشاريع</Label>
                  <p className="text-sm text-gray-600">تحديثات حول المشاريع التي تعمل عليها</p>
                </div>
                <Switch id="project-notifications" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="team-notifications">إشعارات الفريق</Label>
                  <p className="text-sm text-gray-600">تحديثات من أعضاء الفريق</p>
                </div>
                <Switch id="team-notifications" />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="marketing-notifications">إشعارات تسويقية</Label>
                  <p className="text-sm text-gray-600">أخبار المنتج والتحديثات</p>
                </div>
                <Switch id="marketing-notifications" />
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                إعدادات الخصوصية
              </CardTitle>
              <CardDescription>
                التحكم في خصوصية معلوماتك
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="profile-visibility">إظهار الملف الشخصي</Label>
                  <p className="text-sm text-gray-600">السماح للآخرين برؤية ملفك الشخصي</p>
                </div>
                <Switch id="profile-visibility" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="activity-status">حالة النشاط</Label>
                  <p className="text-sm text-gray-600">إظهار حالة النشاط للآخرين</p>
                </div>
                <Switch id="activity-status" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="contact-info">معلومات التواصل</Label>
                  <p className="text-sm text-gray-600">السماح للآخرين برؤية معلومات التواصل</p>
                </div>
                <Switch id="contact-info" />
              </div>
            </CardContent>
          </Card>

          {/* Account Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">إجراءات الحساب</CardTitle>
              <CardDescription>
                إجراءات حساسة متعلقة بحسابك
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div>
                  <h4 className="font-medium text-red-800">تعطيل الحساب</h4>
                  <p className="text-sm text-red-600">تعطيل مؤقت للحساب</p>
                </div>
                <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                  تعطيل
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div>
                  <h4 className="font-medium text-red-800">حذف الحساب</h4>
                  <p className="text-sm text-red-600">حذف نهائي للحساب وجميع البيانات</p>
                </div>
                <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                  حذف
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Development Notice */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900">قيد التطوير</h3>
                  <p className="text-blue-700 text-sm">
                    هذه الصفحة قيد التطوير. سيتم إضافة المزيد من الإعدادات والخيارات قريباً.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
