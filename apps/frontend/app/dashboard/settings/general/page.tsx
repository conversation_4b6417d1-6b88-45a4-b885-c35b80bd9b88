'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Settings, 
  Building,
  Mail,
  Phone,
  Globe,
  Shield,
  Bell,
  Palette,
  Database,
  Save
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function GeneralSettingsPage() {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]}>
      <UnifiedLayout>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">الإعدادات العامة</h1>
              <p className="text-gray-600 mt-1">إعدادات النظام والشركة العامة</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                قيد التطوير
              </Badge>
              <Button>
                <Save className="h-4 w-4 ml-2" />
                حفظ التغييرات
              </Button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  معلومات الشركة
                </CardTitle>
                <CardDescription>
                  المعلومات الأساسية للشركة والوكالة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="company-name">اسم الشركة</Label>
                    <Input id="company-name" defaultValue="MTBRMG Digital Agency" />
                  </div>
                  <div>
                    <Label htmlFor="company-name-ar">اسم الشركة بالعربية</Label>
                    <Input id="company-name-ar" defaultValue="وكالة MTBRMG الرقمية" />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="company-description">وصف الشركة</Label>
                  <Textarea 
                    id="company-description" 
                    defaultValue="وكالة رقمية متخصصة في تطوير المواقع والتطبيقات والتسويق الرقمي"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="company-email">البريد الإلكتروني</Label>
                    <Input id="company-email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="company-phone">رقم الهاتف</Label>
                    <Input id="company-phone" defaultValue="+20 ************" />
                  </div>
                </div>

                <div>
                  <Label htmlFor="company-address">العنوان</Label>
                  <Input id="company-address" defaultValue="القاهرة، مصر" />
                </div>
              </CardContent>
            </Card>

            {/* System Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  إعدادات النظام
                </CardTitle>
                <CardDescription>
                  الإعدادات العامة لتشغيل النظام
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="default-language">اللغة الافتراضية</Label>
                    <Input id="default-language" defaultValue="العربية" disabled />
                  </div>
                  <div>
                    <Label htmlFor="timezone">المنطقة الزمنية</Label>
                    <Input id="timezone" defaultValue="Africa/Cairo" disabled />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="currency">العملة الافتراضية</Label>
                    <Input id="currency" defaultValue="جنيه مصري (EGP)" disabled />
                  </div>
                  <div>
                    <Label htmlFor="date-format">تنسيق التاريخ</Label>
                    <Input id="date-format" defaultValue="DD/MM/YYYY" disabled />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  إعدادات الأمان
                </CardTitle>
                <CardDescription>
                  إعدادات الأمان وحماية النظام
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="two-factor">المصادقة الثنائية</Label>
                    <p className="text-sm text-gray-600">تفعيل المصادقة الثنائية لجميع المستخدمين</p>
                  </div>
                  <Switch id="two-factor" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="password-policy">سياسة كلمات المرور القوية</Label>
                    <p className="text-sm text-gray-600">إجبار المستخدمين على استخدام كلمات مرور قوية</p>
                  </div>
                  <Switch id="password-policy" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="session-timeout">انتهاء الجلسة التلقائي</Label>
                    <p className="text-sm text-gray-600">تسجيل خروج تلقائي بعد فترة عدم نشاط</p>
                  </div>
                  <Switch id="session-timeout" defaultChecked />
                </div>

                <div>
                  <Label htmlFor="session-duration">مدة الجلسة (بالدقائق)</Label>
                  <Input id="session-duration" type="number" defaultValue="60" className="w-32" />
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  إعدادات الإشعارات
                </CardTitle>
                <CardDescription>
                  إعدادات الإشعارات والتنبيهات
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-notifications">إشعارات البريد الإلكتروني</Label>
                    <p className="text-sm text-gray-600">إرسال إشعارات عبر البريد الإلكتروني</p>
                  </div>
                  <Switch id="email-notifications" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="push-notifications">الإشعارات الفورية</Label>
                    <p className="text-sm text-gray-600">إشعارات فورية في المتصفح</p>
                  </div>
                  <Switch id="push-notifications" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="task-reminders">تذكيرات المهام</Label>
                    <p className="text-sm text-gray-600">تذكيرات تلقائية للمهام المستحقة</p>
                  </div>
                  <Switch id="task-reminders" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="project-updates">تحديثات المشاريع</Label>
                    <p className="text-sm text-gray-600">إشعارات عند تحديث حالة المشاريع</p>
                  </div>
                  <Switch id="project-updates" defaultChecked />
                </div>
              </CardContent>
            </Card>

            {/* Appearance Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  إعدادات المظهر
                </CardTitle>
                <CardDescription>
                  تخصيص مظهر النظام والواجهة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="dark-mode">الوضع الداكن</Label>
                    <p className="text-sm text-gray-600">تفعيل الوضع الداكن افتراضياً</p>
                  </div>
                  <Switch id="dark-mode" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="rtl-layout">التخطيط من اليمين لليسار</Label>
                    <p className="text-sm text-gray-600">دعم اللغة العربية والتخطيط RTL</p>
                  </div>
                  <Switch id="rtl-layout" defaultChecked disabled />
                </div>

                <div>
                  <Label htmlFor="primary-color">اللون الأساسي</Label>
                  <div className="flex items-center gap-2 mt-2">
                    <div className="w-8 h-8 bg-brand-primary rounded border"></div>
                    <Input id="primary-color" defaultValue="#6e1cc8" className="w-32" disabled />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Development Notice */}
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <Settings className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900">قيد التطوير</h3>
                    <p className="text-blue-700 text-sm">
                      هذه الصفحة قيد التطوير. سيتم إضافة المزيد من الإعدادات والخيارات قريباً.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </UnifiedLayout>
    </RoleGuard>
  );
}
