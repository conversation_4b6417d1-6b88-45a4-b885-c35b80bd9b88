'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  FolderOpen,
  CheckSquare,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function AnalyticsPage() {
  // Demo analytics data
  const analyticsData = {
    revenue: {
      current: 125000,
      previous: 98000,
      growth: 27.6,
      target: 150000
    },
    projects: {
      completed: 8,
      inProgress: 12,
      planning: 5,
      total: 25
    },
    clients: {
      total: 24,
      new: 3,
      active: 21,
      retention: 87.5
    },
    team: {
      productivity: 92,
      utilization: 85,
      satisfaction: 4.2
    }
  };

  const monthlyData = [
    { month: 'يناير', revenue: 85000, projects: 6, clients: 18 },
    { month: 'فبراير', revenue: 92000, projects: 7, clients: 19 },
    { month: 'مارس', revenue: 98000, projects: 8, clients: 21 },
    { month: 'أبريل', revenue: 125000, projects: 10, clients: 24 },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.SALES_MANAGER, UserRole.MEDIA_BUYER]}>
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
              <p className="text-gray-600 mt-1">تحليل شامل لأداء الوكالة والمؤشرات الرئيسية</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="text-blue-600 border-blue-600">
                قيد التطوير
              </Badge>
              <Button variant="outline">
                <Filter className="h-4 w-4 ml-2" />
                تصفية
              </Button>
              <Button>
                <Download className="h-4 w-4 ml-2" />
                تصدير التقرير
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analyticsData.revenue.current)}</div>
                <div className="flex items-center text-xs text-green-600">
                  <TrendingUp className="h-3 w-3 ml-1" />
                  +{analyticsData.revenue.growth}% من الشهر الماضي
                </div>
                <Progress value={(analyticsData.revenue.current / analyticsData.revenue.target) * 100} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  الهدف: {formatCurrency(analyticsData.revenue.target)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المشاريع</CardTitle>
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.projects.total}</div>
                <p className="text-xs text-muted-foreground">
                  {analyticsData.projects.completed} مكتمل، {analyticsData.projects.inProgress} جاري
                </p>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {analyticsData.projects.planning} تخطيط
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">العملاء</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.clients.total}</div>
                <p className="text-xs text-muted-foreground">
                  +{analyticsData.clients.new} عميل جديد هذا الشهر
                </p>
                <div className="flex items-center text-xs text-green-600 mt-2">
                  <TrendingUp className="h-3 w-3 ml-1" />
                  {analyticsData.clients.retention}% معدل الاحتفاظ
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إنتاجية الفريق</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.team.productivity}%</div>
                <p className="text-xs text-muted-foreground">
                  {analyticsData.team.utilization}% معدل الاستخدام
                </p>
                <Progress value={analyticsData.team.productivity} className="mt-2" />
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Revenue Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  اتجاه الإيرادات
                </CardTitle>
                <CardDescription>
                  الإيرادات الشهرية للأشهر الأربعة الماضية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyData.map((data, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{data.month}</h4>
                        <p className="text-sm text-gray-600">{data.projects} مشروع</p>
                      </div>
                      <div className="text-left">
                        <div className="text-lg font-bold text-green-600">
                          {formatCurrency(data.revenue)}
                        </div>
                        <p className="text-xs text-gray-500">{data.clients} عميل</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Project Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  حالة المشاريع
                </CardTitle>
                <CardDescription>
                  توزيع المشاريع حسب الحالة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-green-800">مكتملة</h4>
                      <p className="text-sm text-green-600">مشاريع منجزة بنجاح</p>
                    </div>
                    <div className="text-2xl font-bold text-green-800">
                      {analyticsData.projects.completed}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-blue-800">قيد التنفيذ</h4>
                      <p className="text-sm text-blue-600">مشاريع جارية</p>
                    </div>
                    <div className="text-2xl font-bold text-blue-800">
                      {analyticsData.projects.inProgress}
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-orange-800">التخطيط</h4>
                      <p className="text-sm text-orange-600">مشاريع في مرحلة التخطيط</p>
                    </div>
                    <div className="text-2xl font-bold text-orange-800">
                      {analyticsData.projects.planning}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                رؤى الأداء
              </CardTitle>
              <CardDescription>
                تحليل مفصل للأداء والتوصيات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-3xl font-bold text-green-600 mb-2">+27.6%</div>
                  <h4 className="font-medium">نمو الإيرادات</h4>
                  <p className="text-sm text-gray-600">مقارنة بالشهر الماضي</p>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">87.5%</div>
                  <h4 className="font-medium">معدل الاحتفاظ بالعملاء</h4>
                  <p className="text-sm text-gray-600">أداء ممتاز</p>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-3xl font-bold text-purple-600 mb-2">4.2/5</div>
                  <h4 className="font-medium">رضا الفريق</h4>
                  <p className="text-sm text-gray-600">تقييم داخلي</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Development Notice */}
          <Card className="mt-8 border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900">قيد التطوير</h3>
                  <p className="text-blue-700 text-sm">
                    هذه الصفحة قيد التطوير. سيتم إضافة المزيد من التقارير والرسوم البيانية التفاعلية قريباً.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    </RoleGuard>
  );
}
